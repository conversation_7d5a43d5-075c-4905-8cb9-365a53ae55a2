# Simple POS - Native Android App

A lightweight, offline-first Point of Sale application for small informal shops, built with <PERSON><PERSON><PERSON> and Jetpack Compose.

## Prerequisites

- **Android Studio** (latest version recommended)
- **JDK 8 or higher**
- **Android SDK** (API level 24+)
- **Physical Android device** or **Android Emulator**

## Setup Instructions

### Option 1: Using Android Studio (Recommended)

1. **Open Android Studio**
2. **Import Project**:
   - Click "Open an existing project"
   - Navigate to the `src/` folder in this repository
   - Select the `src` folder and click "OK"
3. **Sync Project**:
   - Android Studio will automatically sync Gradle files
   - Wait for the sync to complete
4. **Run the App**:
   - Connect your Android device via USB (enable Developer Options & USB Debugging)
   - OR start an Android emulator
   - Click the green "Run" button (▶️) or press `Shift + F10`

### Option 2: Using Command Line

1. **Navigate to the src directory**:
   ```bash
   cd src/
   ```

2. **Build the project**:
   ```bash
   ./gradlew build
   ```

3. **Install on connected device**:
   ```bash
   ./gradlew installDebug
   ```

4. **Run the app**:
   ```bash
   ./gradlew installDebug && adb shell am start -n com.simplepos.app/.MainActivity
   ```

## Project Structure

```
src/
├── app/
│   ├── build.gradle                 # App-level dependencies
│   ├── src/main/
│   │   ├── AndroidManifest.xml      # App configuration
│   │   ├── java/com/simplepos/app/  # Kotlin source code
│   │   └── res/                     # Resources (layouts, strings, etc.)
│   └── proguard-rules.pro
├── build.gradle                     # Project-level build script
├── settings.gradle                  # Project settings
└── gradle.properties               # Gradle configuration
```

## Features

- ✅ **Offline-first**: Works without internet connection
- ✅ **Product Management**: Add, edit, delete products
- ✅ **Sales Recording**: Quick sales with automatic calculations
- ✅ **Reports**: Daily and weekly sales summaries
- ✅ **Expense Tracking**: Track business expenses
- ✅ **Large Touch Buttons**: Optimized for ease of use
- ✅ **Local Database**: All data stored locally using Room/SQLite

## Troubleshooting

### Common Issues:

1. **"SDK not found"**:
   - Install Android SDK through Android Studio
   - Set ANDROID_HOME environment variable

2. **"Device not found"**:
   - Enable Developer Options on your Android device
   - Enable USB Debugging
   - Install device drivers if needed

3. **Build fails**:
   - Clean and rebuild: `./gradlew clean build`
   - Check internet connection for dependency downloads

### Getting Help:

- Check Android Studio's "Build" tab for detailed error messages
- Ensure your device/emulator is running Android 7.0 (API 24) or higher
- Make sure USB debugging is enabled on your device

## Development Notes

This is a **native Android application** built with:
- **Kotlin** programming language
- **Jetpack Compose** for modern UI
- **Room Database** for local data storage
- **MVVM Architecture** with Repository pattern

**Note**: This is NOT a React Native app - no npm, Node.js, or JavaScript server required!
