<resources>
    <string name="app_name">Simple POS</string>
    
    <!-- Navigation -->
    <string name="nav_products">Products</string>
    <string name="nav_sales">Sales</string>
    <string name="nav_reports">Reports</string>
    <string name="nav_expenses">Expenses</string>
    
    <!-- Products -->
    <string name="products_title">Products</string>
    <string name="add_product">Add Product</string>
    <string name="edit_product">Edit Product</string>
    <string name="product_name">Product Name</string>
    <string name="product_price">Price</string>
    <string name="product_stock">Stock Quantity</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="delete">Delete</string>
    
    <!-- Sales -->
    <string name="sales_title">New Sale</string>
    <string name="select_product">Select Product</string>
    <string name="quantity">Quantity</string>
    <string name="total">Total</string>
    <string name="complete_sale">Complete Sale</string>
    <string name="sale_completed">Sale completed successfully!</string>
    
    <!-- Reports -->
    <string name="reports_title">Reports</string>
    <string name="daily_report">Daily Report</string>
    <string name="weekly_report">Weekly Report</string>
    <string name="total_sales">Total Sales</string>
    <string name="total_items_sold">Total Items Sold</string>
    <string name="best_selling_products">Best Selling Products</string>
    
    <!-- Expenses -->
    <string name="expenses_title">Expenses</string>
    <string name="add_expense">Add Expense</string>
    <string name="expense_description">Description</string>
    <string name="expense_amount">Amount</string>
    <string name="expense_added">Expense added successfully!</string>
    
    <!-- Common -->
    <string name="currency_symbol">R</string>
    <string name="empty_list">No items found</string>
    <string name="error_occurred">An error occurred</string>
    <string name="confirm_delete">Are you sure you want to delete this item?</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
</resources>
