# EditorConfig is awesome: https://editorconfig.org
root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

# Markdown files (no trimming whitespace to preserve formatting)
[*.md]
trim_trailing_whitespace = false

# JSON, YAML, and similar (2 spaces is common)
[*.{json,yml,yaml}]
indent_style = space
indent_size = 2

# Python & Lua
[*.{py,lua}]
indent_style = space
indent_size = 4
max_line_length = 88  # black default, optional

# Java, C, C++, C#
[*.{java,c,cc,cpp,cxx,h,hpp,inl,tli,cs}]
indent_style = space
indent_size = 4

# Rust, Go, Ruby, PHP
[*.{rs,go,rb,php}]
indent_style = space
indent_size = 4

# HLSL & GLSL
[*.{hlsl,glsl}]
indent_style = space
indent_size = 4

# JavaScript & TypeScript
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2

# HTML, CSS, SCSS
[*.{html,htm,css,scss}]
indent_style = space
indent_size = 2

# Shell scripts (POSIX)
[*.sh]
end_of_line = lf
indent_style = space
indent_size = 4

# Windows batch files (must use CRLF)
[*.{bat,cmd}]
end_of_line = crlf
indent_style = tab

# Makefiles (MUST use tabs)
[Makefile]
end_of_line = lf
indent_style = tab
