{"name": "TownshipPOSMobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.25.0", "react-native-safe-area-context": "^4.7.4", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "react-native-vector-icons": "^10.0.0", "react-native-paper": "^5.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0"}, "engines": {"node": ">=16"}}