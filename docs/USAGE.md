# Usage Guide

## Getting Started

After setting up the system (see SETUP.md), you can access the Township POS System at `http://localhost:3000`.

## System Navigation

The system has three main sections accessible via the top navigation:

### 1. POS (Point of Sale)
**Purpose**: Process customer transactions

**How to use**:
1. Select an item from the dropdown menu
2. Enter the quantity being sold
3. Choose the payment method (Cash, Mobile Money, QR Code, Card)
4. Click "Process Sale"
5. The system will update inventory and credit score automatically

**Features**:
- Real-time inventory checking
- Multiple payment method support
- Automatic stock deduction
- Instant credit score updates

### 2. Inventory Management
**Purpose**: Manage your shop's stock

**How to use**:
1. **Add New Items**: Fill in item name, price, and quantity, then click "Add Item"
2. **View Current Stock**: See all items with their prices and quantities
3. **Monitor Stock Levels**: Items with less than 5 units show "Low Stock" warning

**Features**:
- Easy item addition
- Stock level monitoring
- Low stock alerts
- Price management

### 3. Dashboard
**Purpose**: View business analytics and credit information

**What you'll see**:
- **Township Credit Score**: Your current score and rating
- **Loan Eligibility**: Available loan amounts and interest rates
- **Payment Methods Chart**: Visual breakdown of how customers pay
- **Recent Sales**: List of your latest transactions

**Credit Score Ratings**:
- **Excellent (80-100)**: Up to R5,000 loans at 12% interest
- **Good (60-79)**: Up to R3,000 loans at 15% interest
- **Fair (40-59)**: Up to R1,500 loans at 18% interest
- **Poor (0-39)**: Up to R500 loans at 22% interest

## Daily Workflow

### Morning Setup
1. Check inventory levels in the Inventory tab
2. Add any new stock received
3. Note any low-stock items for reordering

### During Business Hours
1. Use the POS tab for all customer transactions
2. Select correct items and quantities
3. Choose appropriate payment methods
4. Process sales quickly and efficiently

### End of Day
1. Check the Dashboard for daily performance
2. Review total sales and transaction count
3. Monitor credit score improvements
4. Plan for next day's inventory needs

## Tips for Better Credit Scores

### Increase Transaction Volume
- Process more sales daily
- Encourage customers to buy multiple items
- Offer bundle deals

### Diversify Payment Methods
- Accept mobile money payments
- Use QR code payments when possible
- Encourage digital payments over cash

### Maintain Consistent Sales
- Keep regular business hours
- Maintain good customer service
- Stock popular items consistently

### Track Everything
- Record all sales through the POS system
- Don't process cash-only sales outside the system
- Keep accurate inventory records

## Loan Application Process

1. **Check Eligibility**: View your current loan eligibility on the Dashboard
2. **Apply for Loan**: Click "Apply for Loan" button (feature coming soon)
3. **Provide Documentation**: Submit required business documents
4. **Await Approval**: Lender reviews your credit score and application
5. **Receive Funds**: Approved loans are disbursed to your account
6. **Repay on Time**: Timely repayments improve your credit score

## Troubleshooting

### Common Issues

**"Item not found" error**:
- Make sure the item exists in your inventory
- Check spelling of item names

**"Not enough stock" error**:
- Verify you have sufficient quantity
- Update inventory if you've restocked

**Credit score not updating**:
- Ensure sales are being processed through the POS
- Check that transactions are being recorded

**Dashboard not loading**:
- Refresh the page
- Check that the backend server is running

### Getting Help

If you encounter issues:
1. Check that both frontend and backend servers are running
2. Verify your internet connection
3. Try refreshing the browser page
4. Contact technical support if problems persist

## Best Practices

### Security
- Keep your system updated
- Use strong passwords
- Regularly backup your data
- Monitor for unusual transactions

### Business Operations
- Train staff on system usage
- Keep physical inventory counts
- Reconcile daily sales
- Plan inventory based on sales data

### Credit Building
- Process all sales through the system
- Maintain consistent business operations
- Pay loans on time
- Keep accurate records

## Data Privacy

- All transaction data is stored locally
- Personal information is protected
- Credit scores are calculated automatically
- Data is only shared with authorized lenders