> *This document serves as a template for you to list all third-party repos, modules, libraries, frameworks and or datasets used and credit their authors.*

> Please fill this out to give proper credit and help judges understand external dependencies.

> List **only the external resources you used directly**. Do **not** include system libraries or standard runtimes (e.g., Python VCRuntime, Java SDK). 

# Acknowledgements

This document lists all **third-party repositories, modules, libraries, frameworks, APIs, and datasets** used in this project.  

---

## 📦 Libraries, Frameworks & Submodules
| Name                 | Link / Repo                                           | Author(s)       | Usage                  |
|----------------------|-------------------------------------------------------|-----------------|------------------------|
| IntricatePointers    | https://github.com/DnA-IntRicate/IntricatePointers    | <PERSON>  | Memory management      |
| fast_obj             | https://github.com/thisistherk/fast_obj               | <PERSON>  | Parsing OBJ files      |

---

## 🌐 APIs & Services
| Name          | Link / Docs                        | Author(s)      | Usage                             |
|---------------|------------------------------------|----------------|-----------------------------------|
| OpenWeather   | https://openweathermap.org/api     | Open Weather   | Fetching weather data             |
| Stripe        | https://stripe.com/docs/api        | Stripe         | Payment handling                  |

---

## 📊 Datasets
| Name          | Source                              | Usage                             |
|---------------|-------------------------------------|-----------------------------------|
| MNIST         | http://yann.lecun.com/exdb/mnist/   | Training/testing machine learning |
